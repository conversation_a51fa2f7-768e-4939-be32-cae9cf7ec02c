@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-family: 'Inter', system-ui, sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5;
  }

  .btn-secondary {
    @apply border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-medium py-3 px-6 rounded-lg transition-all duration-200;
  }

  .section-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 to-secondary-500 bg-clip-text text-transparent;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-2;
  }
}

.hero-pattern {
  background-image: 
    radial-gradient(circle at 1px 1px, rgba(59, 130, 246, 0.15) 1px, transparent 0);
  background-size: 40px 40px;
}

.floating-animation {
  animation: float 6s ease-in-out infinite;
}

.floating-animation:nth-child(2) {
  animation-delay: -2s;
}

.floating-animation:nth-child(3) {
  animation-delay: -4s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(-20px) rotate(-1deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 4s ease-in-out infinite;
}

.animate-bounce-slow {
  animation: bounce 3s ease-in-out infinite;
}

/* Terminal specific animations */
@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.typewriter {
  overflow: hidden;
  border-right: 2px solid #00ff00;
  white-space: nowrap;
  animation: typewriter 2s steps(40, end), blink 1s step-end infinite;
}

.terminal-cursor {
  animation: blink 1s step-end infinite;
}

/* Glitch effect for terminal */
@keyframes glitch {
  0% {
    transform: translate(0);
  }
  20% {
    transform: translate(-2px, 2px);
  }
  40% {
    transform: translate(-2px, -2px);
  }
  60% {
    transform: translate(2px, 2px);
  }
  80% {
    transform: translate(2px, -2px);
  }
  100% {
    transform: translate(0);
  }
}

.glitch {
  animation: glitch 0.3s ease-in-out infinite;
}

/* Enhanced Terminal Styles */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-green-600 {
  scrollbar-color: #059669 transparent;
}

.scrollbar-track-gray-900 {
  scrollbar-color: #059669 #111827;
}

/* Webkit scrollbar styles */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #111827;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #059669;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #047857;
}

/* Matrix animation */
@keyframes matrix-fall {
  0% {
    transform: translateY(-100vh);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

.matrix-char {
  animation: matrix-fall linear infinite;
}

/* Terminal scan lines */
@keyframes scan-lines {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100vh);
  }
}

.scan-lines {
  animation: scan-lines 2s linear infinite;
}

/* Enhanced fade in animation */
@keyframes enhanced-fade-in {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-enhanced-fade-in {
  animation: enhanced-fade-in 0.5s ease-out forwards;
}

/* Terminal glow effect */
.terminal-glow {
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
}

/* Typing indicator */
@keyframes typing-dots {
  0%, 20% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.typing-indicator::after {
  content: '...';
  animation: typing-dots 1.5s infinite;
}
