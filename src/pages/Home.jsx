import React, { useState, useEffect, useRef } from 'react'
import {
  ArrowRight, Shield, Eye, Zap, Users, CheckCircle, Star,
  Code, Search, AlertTriangle, TrendingUp, Award, Clock,
  Globe, Lock, Cpu, Database, FileText, BarChart3, Monitor, Palette,
  Terminal, Activity, Wifi, Server, HardDrive, Network, Power
} from 'lucide-react'

// Enhanced Interactive Terminal Component
const EnhancedTerminal = () => {
  const [currentInput, setCurrentInput] = useState('')
  const [commandHistory, setCommandHistory] = useState([])
  const [historyIndex, setHistoryIndex] = useState(-1)
  const [terminalOutput, setTerminalOutput] = useState([
    { type: 'system', text: 'ChainHawk Security Terminal v3.0.1 - Enhanced Edition' },
    { type: 'system', text: 'Last login: ' + new Date().toLocaleString() + ' on ttys001' },
    { type: 'success', text: '✓ All security modules loaded successfully' },
    { type: 'info', text: 'Type "help" for available commands or "demo" for interactive demo' },
  ])
  const [isTyping, setIsTyping] = useState(false)
  const [matrixChars, setMatrixChars] = useState([])
  const [systemStats, setSystemStats] = useState({
    cpu: 23,
    memory: 67,
    network: 89,
    threats: 0,
    scans: 1337
  })
  const inputRef = useRef(null)
  const terminalRef = useRef(null)

  // Available commands with responses
  const commands = {
    help: () => [
      { type: 'info', text: '=== ChainHawk Security Commands ===' },
      { type: 'command', text: 'help          - Show this help message' },
      { type: 'command', text: 'demo          - Launch interactive security demo' },
      { type: 'command', text: 'scan [target] - Start security scan' },
      { type: 'command', text: 'status        - Show system status' },
      { type: 'command', text: 'monitor       - Real-time threat monitoring' },
      { type: 'command', text: 'matrix        - Enable matrix mode' },
      { type: 'command', text: 'hack [target] - Simulate penetration test' },
      { type: 'command', text: 'nmap [ip]     - Network discovery' },
      { type: 'command', text: 'whoami        - Current user info' },
      { type: 'command', text: 'ps aux        - Show running processes' },
      { type: 'command', text: 'netstat       - Network connections' },
      { type: 'command', text: 'clear         - Clear terminal' },
      { type: 'command', text: 'exit          - Exit terminal' },
    ],

    demo: () => [
      { type: 'success', text: '🚀 Launching ChainHawk Security Demo...' },
      { type: 'info', text: 'Initializing blockchain scanner...' },
      { type: 'success', text: '✓ Connected to Ethereum mainnet' },
      { type: 'success', text: '✓ Connected to Polygon network' },
      { type: 'success', text: '✓ AI threat detection online' },
      { type: 'warning', text: '⚠ Demo mode: No real transactions will be affected' },
    ],

    status: () => [
      { type: 'info', text: '=== System Status ===' },
      { type: 'success', text: `CPU Usage: ${systemStats.cpu}%` },
      { type: 'success', text: `Memory: ${systemStats.memory}%` },
      { type: 'success', text: `Network: ${systemStats.network}% active` },
      { type: 'success', text: `Threats Blocked: ${systemStats.threats}` },
      { type: 'success', text: `Scans Completed: ${systemStats.scans}` },
      { type: 'info', text: 'All systems operational ✓' },
    ],

    whoami: () => [
      { type: 'info', text: 'chainhawk-security-admin' },
      { type: 'info', text: 'Groups: sudo, security, blockchain' },
      { type: 'info', text: 'Clearance Level: MAXIMUM' },
    ],

    clear: () => 'CLEAR',

    matrix: () => {
      setMatrixChars(Array.from({ length: 50 }, () => ({
        char: String.fromCharCode(0x30A0 + Math.random() * 96),
        x: Math.random() * 100,
        y: Math.random() * 100,
        speed: Math.random() * 2 + 1
      })))
      return [{ type: 'success', text: 'Matrix mode activated... Welcome to the real world.' }]
    }
  }

  // Handle command execution
  const executeCommand = (cmd) => {
    const trimmedCmd = cmd.trim().toLowerCase()
    const [command, ...args] = trimmedCmd.split(' ')

    // Add command to history
    setCommandHistory(prev => [...prev, cmd])
    setHistoryIndex(-1)

    // Add command to output
    setTerminalOutput(prev => [...prev, { type: 'input', text: `chainhawk@security:~$ ${cmd}` }])

    if (commands[command]) {
      const response = commands[command](args)
      if (response === 'CLEAR') {
        setTerminalOutput([])
      } else {
        setIsTyping(true)
        // Simulate typing delay
        setTimeout(() => {
          setTerminalOutput(prev => [...prev, ...response])
          setIsTyping(false)
        }, 500)
      }
    } else if (command === 'scan') {
      const target = args[0] || 'localhost'
      setTerminalOutput(prev => [...prev,
        { type: 'info', text: `Scanning ${target}...` },
        { type: 'success', text: '✓ Port 80: Open (HTTP)' },
        { type: 'success', text: '✓ Port 443: Open (HTTPS)' },
        { type: 'warning', text: '⚠ Port 22: Filtered (SSH)' },
        { type: 'success', text: 'Scan complete. No vulnerabilities found.' }
      ])
    } else if (command === 'hack') {
      const target = args[0] || 'target.com'
      setTerminalOutput(prev => [...prev,
        { type: 'warning', text: `Initiating penetration test on ${target}...` },
        { type: 'info', text: 'Running vulnerability assessment...' },
        { type: 'success', text: '✓ SQL injection test: PASSED' },
        { type: 'success', text: '✓ XSS vulnerability test: PASSED' },
        { type: 'success', text: '✓ CSRF protection: ACTIVE' },
        { type: 'success', text: `${target} appears to be secure! 🛡️` }
      ])
    } else if (command === 'nmap') {
      const ip = args[0] || '***********'
      setTerminalOutput(prev => [...prev,
        { type: 'info', text: `Starting Nmap scan on ${ip}...` },
        { type: 'success', text: 'Host is up (0.001s latency)' },
        { type: 'info', text: 'PORT     STATE SERVICE' },
        { type: 'info', text: '22/tcp   open  ssh' },
        { type: 'info', text: '80/tcp   open  http' },
        { type: 'info', text: '443/tcp  open  https' },
      ])
    } else if (trimmedCmd === '') {
      // Empty command, just show prompt
    } else {
      setTerminalOutput(prev => [...prev, { type: 'error', text: `Command not found: ${command}. Type 'help' for available commands.` }])
    }
  }

  // Handle key press
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      executeCommand(currentInput)
      setCurrentInput('')
    } else if (e.key === 'ArrowUp') {
      e.preventDefault()
      if (commandHistory.length > 0) {
        const newIndex = historyIndex === -1 ? commandHistory.length - 1 : Math.max(0, historyIndex - 1)
        setHistoryIndex(newIndex)
        setCurrentInput(commandHistory[newIndex])
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault()
      if (historyIndex !== -1) {
        const newIndex = historyIndex + 1
        if (newIndex >= commandHistory.length) {
          setHistoryIndex(-1)
          setCurrentInput('')
        } else {
          setHistoryIndex(newIndex)
          setCurrentInput(commandHistory[newIndex])
        }
      }
    } else if (e.key === 'Tab') {
      e.preventDefault()
      // Simple auto-complete
      const availableCommands = Object.keys(commands)
      const matches = availableCommands.filter(cmd => cmd.startsWith(currentInput.toLowerCase()))
      if (matches.length === 1) {
        setCurrentInput(matches[0])
      }
    }
  }

  // Auto-scroll to bottom
  useEffect(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight
    }
  }, [terminalOutput])

  // Update system stats periodically
  useEffect(() => {
    const interval = setInterval(() => {
      setSystemStats(prev => ({
        cpu: Math.max(10, Math.min(90, prev.cpu + (Math.random() - 0.5) * 10)),
        memory: Math.max(30, Math.min(95, prev.memory + (Math.random() - 0.5) * 5)),
        network: Math.max(50, Math.min(100, prev.network + (Math.random() - 0.5) * 8)),
        threats: prev.threats + (Math.random() > 0.95 ? 1 : 0),
        scans: prev.scans + (Math.random() > 0.9 ? 1 : 0)
      }))
    }, 2000)
    return () => clearInterval(interval)
  }, [])

  // Matrix animation
  useEffect(() => {
    if (matrixChars.length > 0) {
      const interval = setInterval(() => {
        setMatrixChars(prev => prev.map(char => ({
          ...char,
          y: (char.y + char.speed) % 100
        })))
      }, 100)
      return () => clearInterval(interval)
    }
  }, [matrixChars])

  return (
    <div className="relative bg-black text-green-400 font-mono min-h-screen overflow-hidden">
      {/* Matrix Background */}
      {matrixChars.length > 0 && (
        <div className="absolute inset-0 pointer-events-none opacity-20">
          {matrixChars.map((char, i) => (
            <div
              key={i}
              className="absolute text-green-300 text-sm animate-pulse"
              style={{
                left: `${char.x}%`,
                top: `${char.y}%`,
                transform: 'translateY(-50%)'
              }}
            >
              {char.char}
            </div>
          ))}
        </div>
      )}

      {/* Scanlines Effect */}
      <div className="absolute inset-0 pointer-events-none opacity-10">
        <div className="h-full w-full bg-gradient-to-b from-transparent via-green-500 to-transparent animate-pulse"></div>
      </div>

      {/* Terminal Header */}
      <div className="bg-gray-900 border-b border-green-500 px-4 py-3 flex items-center gap-2 sticky top-0 z-10">
        <div className="flex gap-2">
          <div className="w-3 h-3 bg-red-500 rounded-full hover:bg-red-400 transition-colors cursor-pointer animate-pulse"></div>
          <div className="w-3 h-3 bg-yellow-500 rounded-full hover:bg-yellow-400 transition-colors cursor-pointer animate-pulse"></div>
          <div className="w-3 h-3 bg-green-500 rounded-full hover:bg-green-400 transition-colors cursor-pointer animate-pulse"></div>
        </div>
        <span className="text-green-400 text-sm ml-4 font-bold">ChainHawk Security Terminal</span>

        {/* System Stats */}
        <div className="ml-auto flex items-center gap-4 text-xs">
          <div className="flex items-center gap-1">
            <Cpu className="w-3 h-3" />
            <span className={systemStats.cpu > 80 ? 'text-red-400' : 'text-green-400'}>
              {systemStats.cpu}%
            </span>
          </div>
          <div className="flex items-center gap-1">
            <HardDrive className="w-3 h-3" />
            <span className={systemStats.memory > 90 ? 'text-red-400' : 'text-green-400'}>
              {systemStats.memory}%
            </span>
          </div>
          <div className="flex items-center gap-1">
            <Network className="w-3 h-3" />
            <span className="text-green-400">{systemStats.network}%</span>
          </div>
          <div className="flex items-center gap-1">
            <Shield className="w-3 h-3" />
            <span className="text-cyan-400">{systemStats.threats}</span>
          </div>
        </div>
      </div>

      {/* Terminal Content */}
      <div
        ref={terminalRef}
        className="p-4 h-screen overflow-y-auto scrollbar-thin scrollbar-thumb-green-600 scrollbar-track-gray-900"
        onClick={() => inputRef.current?.focus()}
      >
        {/* ASCII Art Header */}
        <div className="text-green-300 text-xs mb-4 opacity-80">
          <pre>{`
 ██████╗██╗  ██╗ █████╗ ██╗███╗   ██╗██╗  ██╗ █████╗ ██╗    ██╗██╗  ██╗
██╔════╝██║  ██║██╔══██╗██║████╗  ██║██║  ██║██╔══██╗██║    ██║██║ ██╔╝
██║     ███████║███████║██║██╔██╗ ██║███████║███████║██║ █╗ ██║█████╔╝
██║     ██╔══██║██╔══██║██║██║╚██╗██║██╔══██║██╔══██║██║███╗██║██╔═██╗
╚██████╗██║  ██║██║  ██║██║██║ ╚████║██║  ██║██║  ██║╚███╔███╔╝██║  ██╗
 ╚═════╝╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚═╝  ╚═══╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚══╝╚══╝ ╚═╝  ╚═╝
          `}</pre>
        </div>

        {/* Terminal Output */}
        <div className="space-y-1">
          {terminalOutput.map((line, index) => (
            <div key={index} className={`
              ${line.type === 'input' ? 'text-white' : ''}
              ${line.type === 'success' ? 'text-green-400' : ''}
              ${line.type === 'error' ? 'text-red-400' : ''}
              ${line.type === 'warning' ? 'text-yellow-400' : ''}
              ${line.type === 'info' ? 'text-cyan-400' : ''}
              ${line.type === 'command' ? 'text-gray-300' : ''}
              ${line.type === 'system' ? 'text-purple-400' : ''}
            `}>
              {line.text}
            </div>
          ))}

          {isTyping && (
            <div className="text-yellow-400 animate-pulse">
              Processing command...
            </div>
          )}
        </div>

        {/* Current Input Line */}
        <div className="flex items-center mt-2">
          <span className="text-cyan-400">chainhawk@security</span>
          <span className="text-white">:</span>
          <span className="text-blue-400">~</span>
          <span className="text-white">$ </span>
          <input
            ref={inputRef}
            type="text"
            value={currentInput}
            onChange={(e) => setCurrentInput(e.target.value)}
            onKeyDown={handleKeyPress}
            className="bg-transparent border-none outline-none text-green-400 flex-1 font-mono"
            autoFocus
            spellCheck={false}
          />
          <span className="bg-green-400 text-black animate-pulse ml-1">█</span>
        </div>
      </div>
    </div>
  )
}

const Home = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0)
  const [theme, setTheme] = useState('modern') // 'modern' or 'terminal'

  const features = [
    {
      icon: Eye,
      title: 'Smart Scanning',
      description: 'AI-driven code analysis with hawk-eye precision to detect potential security vulnerabilities',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Zap,
      title: 'Lightning Detection',
      description: 'Millisecond response time for rapid threat identification and precise location',
      color: 'from-yellow-500 to-orange-500'
    },
    {
      icon: Shield,
      title: 'Deep Protection',
      description: 'Multi-layered security analysis providing professional remediation advice and best practices',
      color: 'from-green-500 to-emerald-500'
    },
    {
      icon: Users,
      title: 'Team Collaboration',
      description: 'Support team collaboration with real-time sharing of security reports and fix progress',
      color: 'from-purple-500 to-pink-500'
    }
  ]

  const stats = [
    { number: '1000+', label: 'Audited Projects', icon: FileText },
    { number: '50K+', label: 'Vulnerabilities Found', icon: AlertTriangle },
    { number: '99.9%', label: 'Accuracy Rate', icon: Award },
    { number: '24/7', label: 'Monitoring Service', icon: Clock }
  ]

  const technologies = [
    { name: 'Solidity', icon: Code, description: 'Smart Contract Static Analysis' },
    { name: 'Rust', icon: Cpu, description: 'High-Performance Detection Engine' },
    { name: 'Cloud', icon: Globe, description: 'Cloud-Native Architecture' }
  ]

  const testimonials = [
    {
      name: 'Alex Chen',
      role: 'CTO of DeFi Project',
      company: 'CryptoFinance',
      content: 'ChainHawk helped us discover 17 critical security vulnerabilities, preventing potential major losses. Professional, efficient, and trustworthy!',
      avatar: '👨‍💻'
    },
    {
      name: 'Sarah Kim',
      role: 'Blockchain Security Expert',
      company: 'BlockSafe',
      content: 'As a security expert, I highly recommend ChainHawk. Its detection accuracy and report quality have reached industry-leading standards.',
      avatar: '👩‍💼'
    },
    {
      name: 'Michael Johnson',
      role: 'NFT Platform Founder',
      company: 'ArtChain',
      content: 'After using ChainHawk, our smart contract security has significantly improved, and user trust has increased dramatically.',
      avatar: '🧑‍🎨'
    }
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [])

  const services = [
    {
      title: 'Smart Contract Audit',
      description: 'Comprehensive smart contract code audit services to identify potential security vulnerabilities and logic errors',
      features: ['Static Code Analysis', 'Dynamic Testing', 'Formal Verification', 'Detailed Reports'],
      icon: Code,
      color: 'from-blue-600 to-blue-800'
    },
    {
      title: 'Real-time Monitoring',
      description: '24/7 real-time monitoring of your smart contracts to detect abnormal behavior promptly',
      features: ['Real-time Alerts', 'Anomaly Detection', 'Performance Monitoring', 'Risk Assessment'],
      icon: BarChart3,
      color: 'from-green-600 to-green-800'
    },
    {
      title: 'Security Consulting',
      description: 'Professional blockchain security consulting services providing comprehensive security guidance for your projects',
      features: ['Architecture Design', 'Best Practices', 'Risk Assessment', 'Training Services'],
      icon: Shield,
      color: 'from-purple-600 to-purple-800'
    }
  ]

  // Modern Style Component
  const ModernHome = () => (
    <div className="overflow-hidden">
      {/* Hero Section - Split Screen Design */}
      <section className="relative min-h-screen bg-black">
        {/* Left Side - Content */}
        <div className="absolute inset-0 grid grid-cols-1 lg:grid-cols-2">
          {/* Content Panel */}
          <div className="relative z-20 flex items-center justify-center bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 p-8 lg:p-16">
            {/* Floating UI Elements */}
            <div className="absolute top-8 right-8 w-3 h-3 bg-cyan-400 rounded-full animate-pulse"></div>
            <div className="absolute bottom-8 left-8 w-2 h-2 bg-purple-400 rounded-full animate-pulse" style={{animationDelay: '1s'}}></div>

            <div className="max-w-lg">
              {/* Floating Logo */}
              <div className="relative mb-8">
                <div className="absolute -top-4 -left-4 w-16 h-16 bg-cyan-400/20 rounded-2xl blur-xl"></div>
                <div className="relative w-16 h-16 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-2xl flex items-center justify-center shadow-2xl">
                  <Shield className="w-8 h-8 text-white" />
                </div>
              </div>

              {/* Title with Creative Typography */}
              <div className="mb-8">
                <div className="text-sm text-cyan-400 font-mono mb-2 tracking-wider">// BLOCKCHAIN_SECURITY</div>
                <h1 className="text-5xl lg:text-6xl font-black text-white leading-none mb-4">
                  Chain
                  <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 animate-pulse">
                    Hawk
                  </span>
                </h1>
                <div className="flex items-center gap-2 text-slate-400">
                  <div className="w-8 h-px bg-gradient-to-r from-cyan-400 to-transparent"></div>
                  <span className="text-sm font-mono">AI-POWERED SECURITY</span>
                </div>
              </div>

              {/* Description */}
              <p className="text-lg text-slate-300 mb-8 leading-relaxed">
                AI-driven security detection with hawk-eye precision,
                <span className="text-cyan-400 font-semibold">real-time protection</span>
                for your blockchain project security
              </p>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <button className="border border-slate-600 text-slate-300 hover:text-white hover:border-cyan-400 font-semibold py-4 px-8 rounded-xl transition-all duration-300">
                  View Demo
                </button>
                <button className="border border-slate-600 text-slate-300 hover:text-white hover:border-purple-400 font-semibold py-4 px-8 rounded-xl transition-all duration-300">
                  Learn More
                </button>
              </div>

              {/* Mini Stats */}
              <div className="grid grid-cols-3 gap-4 text-center">
                <div className="group cursor-pointer">
                  <div className="text-2xl font-bold text-cyan-400 group-hover:scale-110 transition-transform">1000+</div>
                  <div className="text-xs text-slate-500 uppercase tracking-wider">Projects</div>
                </div>
                <div className="group cursor-pointer">
                  <div className="text-2xl font-bold text-blue-400 group-hover:scale-110 transition-transform">50K+</div>
                  <div className="text-xs text-slate-500 uppercase tracking-wider">Vulnerabilities</div>
                </div>
                <div className="group cursor-pointer">
                  <div className="text-2xl font-bold text-purple-400 group-hover:scale-110 transition-transform">99.9%</div>
                  <div className="text-xs text-slate-500 uppercase tracking-wider">Accuracy</div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Visual */}
          <div className="relative bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center">
            {/* Animated Background */}
            <div className="absolute inset-0">
              <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40"></div>

              {/* Floating Geometric Shapes */}
              <div className="absolute top-1/4 left-1/4 w-32 h-32 border border-cyan-400/20 rounded-2xl rotate-12 animate-float"></div>
              <div className="absolute top-1/2 right-1/4 w-24 h-24 border border-purple-400/20 rounded-full animate-float" style={{animationDelay: '2s'}}></div>
              <div className="absolute bottom-1/4 left-1/3 w-20 h-20 border border-blue-400/20 rotate-45 animate-float" style={{animationDelay: '4s'}}></div>
            </div>

            {/* Central Visual Element */}
            <div className="relative z-10">
              <div className="relative w-80 h-80">
                {/* Main Circle */}
                <div className="absolute inset-0 rounded-full bg-gradient-to-br from-cyan-400/20 to-purple-400/20 backdrop-blur-xl border border-white/10 animate-pulse"></div>

                {/* Orbiting Elements */}
                <div className="absolute inset-0 animate-spin" style={{animationDuration: '20s'}}>
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-cyan-400 rounded-full"></div>
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 w-3 h-3 bg-purple-400 rounded-full"></div>
                  <div className="absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-blue-400 rounded-full"></div>
                  <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-pink-400 rounded-full"></div>
                </div>

                {/* Center Icon */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-20 h-20 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-2xl">
                    <Eye className="w-10 h-10 text-white" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section - Bento Grid Layout */}
      <section className="py-24 bg-white relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-cyan-100 to-blue-100 rounded-full blur-3xl opacity-30"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full blur-3xl opacity-30"></div>

        <div className="section-container relative z-10">
          {/* Section Header */}
          <div className="mb-16">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></div>
              <span className="text-sm font-mono text-slate-500 uppercase tracking-wider">CORE_FEATURES</span>
            </div>
            <h2 className="text-4xl lg:text-6xl font-black text-slate-900 mb-4">
              Why Choose
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-600 to-purple-600">
                ChainHawk
              </span>
            </h2>
          </div>

          {/* Bento Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-auto lg:h-[600px]">
            {/* Large Feature Card */}
            <div className="lg:col-span-2 lg:row-span-2 group relative bg-gradient-to-br from-slate-900 to-blue-900 rounded-3xl p-8 lg:p-12 overflow-hidden">
              {/* Background Pattern */}
              <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2240%22%20height%3D%2240%22%20viewBox%3D%220%200%2040%2040%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.05%22%3E%3Ccircle%20cx%3D%2220%22%20cy%3D%2220%22%20r%3D%221%22/%3E%3C/g%3E%3C/svg%3E')] opacity-50"></div>

              {/* Floating Elements */}
              <div className="absolute top-8 right-8 w-20 h-20 border border-cyan-400/20 rounded-2xl rotate-12 animate-float"></div>
              <div className="absolute bottom-8 left-8 w-16 h-16 border border-purple-400/20 rounded-full animate-float" style={{animationDelay: '2s'}}></div>

              <div className="relative z-10 h-full flex flex-col justify-between">
                <div>
                  <div className="w-16 h-16 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Eye className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-3xl lg:text-4xl font-bold text-white mb-4">
                    AI Smart Scanning
                  </h3>
                  <p className="text-xl text-slate-300 leading-relaxed mb-8">
                    Utilizing cutting-edge machine learning algorithms to identify security vulnerabilities and potential risks in smart contracts with hawk-eye precision
                  </p>
                </div>

                <div className="flex items-center gap-4">
                  <div className="flex -space-x-2">
                    <div className="w-8 h-8 bg-cyan-400 rounded-full border-2 border-white"></div>
                    <div className="w-8 h-8 bg-blue-400 rounded-full border-2 border-white"></div>
                    <div className="w-8 h-8 bg-purple-400 rounded-full border-2 border-white"></div>
                  </div>
                  <span className="text-sm text-slate-400">50,000+ vulnerabilities detected</span>
                </div>
              </div>
            </div>

            {/* Small Feature Cards */}
            <div className="space-y-6">
              {features.slice(1, 3).map((feature, index) => (
                <div key={index} className="group relative bg-white border border-slate-200 rounded-2xl p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className={`w-12 h-12 bg-gradient-to-br ${feature.color} rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <feature.icon className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-slate-600 text-sm leading-relaxed">
                    {feature.description}
                  </p>

                  {/* Hover Effect */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-5 rounded-2xl transition-opacity duration-300`}></div>
                </div>
              ))}
            </div>

            {/* Bottom Feature Card */}
            <div className="lg:col-span-3 group relative bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl p-8 overflow-hidden">
              <div className="absolute inset-0 bg-black/20"></div>
              <div className="relative z-10 flex flex-col lg:flex-row items-center justify-between">
                <div className="flex items-center gap-6 mb-6 lg:mb-0">
                  <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                    <Users className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-white mb-2">
                      Team Collaboration
                    </h3>
                    <p className="text-purple-100">
                      Support team collaboration with real-time sharing of security reports and fix progress
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white">1000+</div>
                    <div className="text-sm text-purple-200">Active Teams</div>
                  </div>
                  <div className="w-px h-12 bg-white/20"></div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white">24/7</div>
                    <div className="text-sm text-purple-200">Real-time Monitoring</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Technology Stack Section - Floating Cards */}
      <section className="py-24 bg-slate-900 relative overflow-hidden">
        {/* Background Animation */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-cyan-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
        </div>

        <div className="section-container relative z-10">
          {/* Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 mb-6">
              <Cpu className="w-6 h-6 text-cyan-400" />
              <span className="text-sm font-mono text-cyan-400 uppercase tracking-wider">TECH_STACK</span>
            </div>
            <h2 className="text-4xl lg:text-5xl font-black text-white mb-4">
              Cutting-edge Technology
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-400">
                Drives Innovation
              </span>
            </h2>
          </div>

          {/* Floating Tech Cards */}
          <div className="relative">
            {/* Tech Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
              {technologies.map((tech, index) => (
                <div
                  key={index}
                  className="group relative"
                  style={{
                    animationDelay: `${index * 0.2}s`
                  }}
                >

                  {/* Tech Card */}
                  <div className="relative bg-slate-800/50 backdrop-blur-xl border border-slate-700 rounded-2xl p-6 group-hover:border-cyan-400/50 transition-all duration-300 group-hover:scale-105 group-hover:-translate-y-2">
                    {/* Glow Effect */}
                    <div className="absolute inset-0 bg-gradient-to-br from-cyan-400/5 to-purple-400/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                    {/* Icon */}
                    <div className="relative w-16 h-16 bg-gradient-to-br from-slate-700 to-slate-600 rounded-xl flex items-center justify-center mb-4 group-hover:bg-gradient-to-br group-hover:from-cyan-500 group-hover:to-blue-600 transition-all duration-300">
                      <tech.icon className="w-8 h-8 text-slate-300 group-hover:text-white transition-colors duration-300" />
                    </div>

                    {/* Content */}
                    <h3 className="text-xl font-bold text-white mb-2 group-hover:text-cyan-400 transition-colors duration-300">
                      {tech.name}
                    </h3>
                    <p className="text-slate-400 text-sm leading-relaxed">
                      {tech.description}
                    </p>

                    {/* Status Indicator */}
                    <div className="flex items-center gap-2 mt-4">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-xs text-slate-500 font-mono">ACTIVE</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Bottom Stats */}
          <div className="mt-20 grid grid-cols-2 lg:grid-cols-4 gap-8 text-center">
            <div className="group cursor-pointer">
              <div className="text-3xl font-bold text-cyan-400 group-hover:scale-110 transition-transform">99.9%</div>
              <div className="text-sm text-slate-500 uppercase tracking-wider">Detection Accuracy</div>
            </div>
            <div className="group cursor-pointer">
              <div className="text-3xl font-bold text-blue-400 group-hover:scale-110 transition-transform">&lt;1s</div>
              <div className="text-sm text-slate-500 uppercase tracking-wider">Response Time</div>
            </div>
            <div className="group cursor-pointer">
              <div className="text-3xl font-bold text-purple-400 group-hover:scale-110 transition-transform">24/7</div>
              <div className="text-sm text-slate-500 uppercase tracking-wider">Real-time Monitoring</div>
            </div>
            <div className="group cursor-pointer">
              <div className="text-3xl font-bold text-pink-400 group-hover:scale-110 transition-transform">AI</div>
              <div className="text-sm text-slate-500 uppercase tracking-wider">Smart Analysis</div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Preview - Staggered Layout */}
      <section className="py-24 bg-gradient-to-br from-slate-50 via-white to-blue-50 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-1/4 right-0 w-64 h-64 bg-gradient-to-br from-blue-200 to-cyan-200 rounded-full blur-3xl opacity-30"></div>
          <div className="absolute bottom-1/4 left-0 w-64 h-64 bg-gradient-to-br from-purple-200 to-pink-200 rounded-full blur-3xl opacity-30"></div>
        </div>

        <div className="section-container relative z-10">
          {/* Header */}
          <div className="mb-20">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-pulse"></div>
              <span className="text-sm font-mono text-slate-500 uppercase tracking-wider">CORE_SERVICES</span>
            </div>
            <h2 className="text-4xl lg:text-6xl font-black text-slate-900 mb-6">
              Core Services
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                Comprehensive Protection
              </span>
            </h2>
          </div>

          {/* Staggered Services */}
          <div className="space-y-24">
            {services.map((service, index) => (
              <div
                key={index}
                className={`flex flex-col lg:flex-row items-center gap-12 ${
                  index % 2 === 1 ? 'lg:flex-row-reverse' : ''
                }`}
              >
                {/* Content Side */}
                <div className="flex-1 space-y-6">
                  {/* Service Badge */}
                  <div className="inline-flex items-center gap-3 bg-white/80 backdrop-blur-sm border border-slate-200 rounded-full px-4 py-2">
                    <div className={`w-8 h-8 bg-gradient-to-br ${service.color} rounded-full flex items-center justify-center`}>
                      <service.icon className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-sm font-semibold text-slate-700">0{index + 1}</span>
                  </div>

                  {/* Title */}
                  <h3 className="text-3xl lg:text-4xl font-bold text-slate-900">
                    {service.title}
                  </h3>

                  {/* Description */}
                  <p className="text-xl text-slate-600 leading-relaxed">
                    {service.description}
                  </p>

                  {/* Features Grid */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {service.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center gap-3 group">
                        <div className="w-6 h-6 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
                          <CheckCircle className="w-4 h-4 text-white" />
                        </div>
                        <span className="text-slate-700 font-medium">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* CTA */}
                  <div className="pt-4">
                    <button className={`group bg-gradient-to-r ${service.color} text-white font-semibold py-4 px-8 rounded-xl hover:shadow-xl transition-all duration-300 hover:scale-105`}>
                      <span className="flex items-center">
                        Learn More
                        <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                      </span>
                    </button>
                  </div>
                </div>

                {/* Visual Side */}
                <div className="flex-1 relative">
                  <div className="relative w-full max-w-md mx-auto">
                    {/* Main Card */}
                    <div className={`relative bg-gradient-to-br ${service.color} rounded-3xl p-8 shadow-2xl transform rotate-3 hover:rotate-0 transition-transform duration-500`}>
                      {/* Background Pattern */}
                      <div className="absolute inset-0 bg-white/10 rounded-3xl"></div>

                      {/* Content */}
                      <div className="relative z-10 text-white">
                        <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mb-6 backdrop-blur-sm">
                          <service.icon className="w-8 h-8 text-white" />
                        </div>

                        <h4 className="text-2xl font-bold mb-4">{service.title}</h4>

                        {/* Mock Dashboard */}
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-sm opacity-80">Detection Progress</span>
                            <span className="text-sm font-semibold">98%</span>
                          </div>
                          <div className="w-full bg-white/20 rounded-full h-2">
                            <div className="bg-white rounded-full h-2 w-[98%]"></div>
                          </div>

                          <div className="grid grid-cols-2 gap-4 mt-6">
                            <div className="text-center">
                              <div className="text-2xl font-bold">24</div>
                              <div className="text-xs opacity-80">Issues Found</div>
                            </div>
                            <div className="text-center">
                              <div className="text-2xl font-bold">99.9%</div>
                              <div className="text-xs opacity-80">Accuracy</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Floating Elements */}
                    <div className="absolute -top-4 -right-4 w-8 h-8 bg-yellow-400 rounded-full animate-bounce"></div>
                    <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-green-400 rounded-full animate-pulse"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section - Floating Cards */}
      <section className="py-24 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 relative overflow-hidden">
        {/* Background Animation */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse" style={{animationDelay: '3s'}}></div>
        </div>

        <div className="section-container relative z-10">
          {/* Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 mb-6">
              <Star className="w-6 h-6 text-yellow-400" />
              <span className="text-sm font-mono text-yellow-400 uppercase tracking-wider">TESTIMONIALS</span>
            </div>
            <h2 className="text-4xl lg:text-5xl font-black text-white mb-4">
              Customer
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-400">
                Testimonials
              </span>
            </h2>
            <p className="text-xl text-slate-400 max-w-3xl mx-auto">
              Trust and recognition from global blockchain projects
            </p>
          </div>

          {/* Testimonials Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
            {testimonials.map((testimonial, index) => (
              <div
                key={index}
                className={`group relative transform transition-all duration-500 hover:scale-105 ${
                  index === 1 ? 'lg:-translate-y-8' : ''
                } ${index === 2 ? 'lg:translate-y-4' : ''}`}
              >
                {/* Card */}
                <div className="relative bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 hover:bg-white/15 transition-all duration-300">
                  {/* Quote Mark */}
                  <div className="absolute -top-4 -left-4 w-12 h-12 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-2xl flex items-center justify-center shadow-xl">
                    <span className="text-2xl text-white font-bold">"</span>
                  </div>

                  {/* Rating Stars */}
                  <div className="flex gap-1 mb-6">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                  </div>

                  {/* Content */}
                  <blockquote className="text-white text-lg leading-relaxed mb-8 font-medium">
                    {testimonial.content}
                  </blockquote>

                  {/* Author */}
                  <div className="flex items-center gap-4">
                    <div className="w-14 h-14 bg-gradient-to-br from-slate-600 to-slate-700 rounded-2xl flex items-center justify-center text-2xl border-2 border-white/20">
                      {testimonial.avatar}
                    </div>
                    <div>
                      <h4 className="text-white font-bold text-lg">
                        {testimonial.name}
                      </h4>
                      <p className="text-slate-300 text-sm">
                        {testimonial.role}
                      </p>
                      <p className="text-slate-400 text-xs">
                        {testimonial.company}
                      </p>
                    </div>
                  </div>

                  {/* Hover Glow Effect */}
                  <div className="absolute inset-0 bg-gradient-to-br from-cyan-400/5 to-purple-400/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>

                {/* Floating Elements */}
                <div className={`absolute -z-10 w-6 h-6 bg-gradient-to-br ${
                  index === 0 ? 'from-cyan-400 to-blue-500' :
                  index === 1 ? 'from-purple-400 to-pink-500' :
                  'from-yellow-400 to-orange-500'
                } rounded-full blur-sm animate-float`}
                style={{
                  top: `${20 + index * 10}%`,
                  right: `${10 + index * 5}%`,
                  animationDelay: `${index * 2}s`
                }}></div>
              </div>
            ))}
          </div>

          {/* Bottom Stats */}
          <div className="text-center">
            <div className="inline-flex items-center gap-8 bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl px-8 py-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-cyan-400">4.9/5</div>
                <div className="text-sm text-slate-400">Average Rating</div>
              </div>
              <div className="w-px h-8 bg-white/20"></div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-400">1000+</div>
                <div className="text-sm text-slate-400">Satisfied Customers</div>
              </div>
              <div className="w-px h-8 bg-white/20"></div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">98%</div>
                <div className="text-sm text-slate-400">Recommendation Rate</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section - Split Design */}
      <section className="relative min-h-screen bg-black overflow-hidden">
        {/* Split Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 min-h-screen">
          {/* Left Side - Content */}
          <div className="relative bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 flex items-center justify-center p-8 lg:p-16">
            {/* Background Elements */}
            <div className="absolute inset-0">
              <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-cyan-500/20 rounded-full blur-3xl animate-pulse"></div>
              <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-purple-500/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
            </div>

            <div className="relative z-10 max-w-lg">
              {/* Badge */}
              <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-4 py-2 mb-8">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm text-white font-mono">READY_TO_START</span>
              </div>

              {/* Title */}
              <h2 className="text-4xl lg:text-5xl font-black text-white mb-6 leading-tight">
                Ready to Start
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-400">
                  Protecting Your Project?
                </span>
              </h2>

              {/* Description */}
              <p className="text-xl text-slate-300 mb-8 leading-relaxed">
                Experience ChainHawk's professional security detection services immediately, let AI safeguard your blockchain project
              </p>

              {/* CTA Buttons */}
              <div className="space-y-4 mb-12">
                <button className="group w-full bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-bold text-lg py-4 px-8 rounded-xl transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1">
                  <span className="flex items-center justify-center">
                    Start Free Trial Now
                    <ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                  </span>
                </button>

                <button className="w-full border-2 border-white/30 text-white hover:bg-white hover:text-slate-900 font-semibold text-lg py-4 px-8 rounded-xl transition-all duration-300 backdrop-blur-sm">
                  Schedule Expert Consultation
                </button>
              </div>

              {/* Features List */}
              <div className="space-y-3">
                <div className="flex items-center gap-3 text-slate-300">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>30-day free trial</span>
                </div>
                <div className="flex items-center gap-3 text-slate-300">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>No credit card required</span>
                </div>
                <div className="flex items-center gap-3 text-slate-300">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>24/7 expert support</span>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Interactive Visual */}
          <div className="relative bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center p-8">
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2240%22%20height%3D%2240%22%20viewBox%3D%220%200%2040%2040%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2220%22%20cy%3D%2220%22%20r%3D%221%22/%3E%3C/g%3E%3C/svg%3E')] opacity-50"></div>

            {/* Interactive Dashboard Mockup */}
            <div className="relative w-full max-w-md">
              {/* Main Dashboard */}
              <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 shadow-2xl">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-xl flex items-center justify-center">
                      <Shield className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-white font-bold">Security Dashboard</h3>
                      <p className="text-slate-400 text-sm">Real-time Monitoring</p>
                    </div>
                  </div>
                  <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="bg-white/5 rounded-2xl p-4 text-center">
                    <div className="text-2xl font-bold text-cyan-400">24</div>
                    <div className="text-xs text-slate-400">Issues Detected</div>
                  </div>
                  <div className="bg-white/5 rounded-2xl p-4 text-center">
                    <div className="text-2xl font-bold text-green-400">99.9%</div>
                    <div className="text-xs text-slate-400">Security Score</div>
                  </div>
                </div>

                {/* Progress */}
                <div className="mb-6">
                  <div className="flex justify-between text-sm text-slate-400 mb-2">
                    <span>Scan Progress</span>
                    <span>87%</span>
                  </div>
                  <div className="w-full bg-white/10 rounded-full h-2">
                    <div className="bg-gradient-to-r from-cyan-400 to-blue-600 rounded-full h-2 w-[87%] animate-pulse"></div>
                  </div>
                </div>

                {/* Action Button */}
                <button className="w-full bg-gradient-to-r from-cyan-500 to-blue-600 text-white font-semibold py-3 rounded-xl hover:shadow-lg transition-all duration-300">
                  View Detailed Report
                </button>
              </div>

              {/* Floating Cards */}
              <div className="absolute -top-4 -right-4 bg-yellow-400 text-black text-xs font-bold px-3 py-2 rounded-xl animate-bounce">
                New Threat Detected!
              </div>
              <div className="absolute -bottom-4 -left-4 bg-green-400 text-black text-xs font-bold px-3 py-2 rounded-xl animate-pulse">
                Fix Completed
              </div>
            </div>

            {/* Orbiting Elements */}
            <div className="absolute inset-0 animate-spin" style={{animationDuration: '30s'}}>
              <div className="absolute top-1/4 left-1/4 w-3 h-3 bg-cyan-400 rounded-full"></div>
              <div className="absolute top-3/4 right-1/4 w-2 h-2 bg-purple-400 rounded-full"></div>
              <div className="absolute top-1/2 left-0 w-2 h-2 bg-pink-400 rounded-full"></div>
              <div className="absolute top-1/2 right-0 w-3 h-3 bg-blue-400 rounded-full"></div>
            </div>
          </div>
        </div>

        {/* Bottom Trust Bar */}
        <div className="absolute bottom-0 left-0 right-0 bg-black/50 backdrop-blur-sm border-t border-white/10 py-4">
          <div className="section-container">
            <div className="flex flex-col lg:flex-row items-center justify-between gap-4">
              <div className="text-slate-400 text-sm">
                Trusted by <span className="text-cyan-400 font-semibold">1000+</span> projects worldwide
              </div>
              <div className="flex items-center gap-8 text-slate-500 text-sm">
                <span className="hover:text-cyan-400 transition-colors cursor-pointer">DeFi</span>
                <span className="hover:text-cyan-400 transition-colors cursor-pointer">NFT</span>
                <span className="hover:text-cyan-400 transition-colors cursor-pointer">GameFi</span>
                <span className="hover:text-cyan-400 transition-colors cursor-pointer">Web3</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )

  return (
    <div className="pt-16">
      {/* Theme Switcher */}
      <div className="fixed top-20 right-4 z-50">
        <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-2 flex gap-2">
          <button
            onClick={() => setTheme('modern')}
            className={`p-2 rounded-lg transition-all duration-300 ${
              theme === 'modern'
                ? 'bg-cyan-500 text-white shadow-lg'
                : 'text-gray-400 hover:text-white hover:bg-white/10'
            }`}
            title="Modern Style"
          >
            <Palette className="w-5 h-5" />
          </button>
          <button
            onClick={() => setTheme('terminal')}
            className={`p-2 rounded-lg transition-all duration-300 ${
              theme === 'terminal'
                ? 'bg-green-500 text-white shadow-lg'
                : 'text-gray-400 hover:text-white hover:bg-white/10'
            }`}
            title="Terminal Style"
          >
            <Monitor className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Render based on theme */}
      {theme === 'modern' ? <ModernHome /> : <EnhancedTerminal />}
    </div>
  )
}

export default Home